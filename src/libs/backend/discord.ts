import { DiscordOrderMessage, OrderWithItems } from "@udoy/utils/types";
import { Env } from "../env";
import { SummaryType } from "@prisma/client";

export async function sendDiscordOrderMessage(order: DiscordOrderMessage) {
  // Construct the order dashboard URL
  const orderUrl = `${Env.NEXT_PUBLIC_FRONTEND_URL}/dashboard/orders/${order.id}`;

  // Customize your embed message
  const embed = {
    title: "অর্ডার এর বিবরন 🛒",
    url: orderUrl, // Make the title clickable
    color: 0xfaa85a, // Orange color
    fields: [
      { name: "Order ID", value: `[#${order.id}](${orderUrl})`, inline: true },
      { name: "Customer", value: order.address.name, inline: true },
      { name: "Zone", value: order.address.zone.nam, inline: true },
      {
        name: "Total",
        value: `৳${(order.subTotal + order.shipping).toLocaleString("bn")}`,
        inline: true,
      },
      {
        name: "Items",
        value: order.orderItems
          .map(
            (item) =>
              `- ${
                item.product.nam || item.product.name
              } x${item.quantity.toLocaleString("bn")}`
          )
          .join("\n"),
      },
    ],
    timestamp: new Date().toISOString(),
    footer: {
      text: "Click the title or Order ID to view details",
    },
  };

  try {
    fetch(Env.DISCORD_WEBHOOK_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        content: "আলহামদুলিল্লাহ, উদয় মার্টে নতুন অর্ডার এসেছে 📦",
        embeds: [embed],
      }),
    });
  } catch (error) {
    console.error("Failed to send Discord notification:", error);
  }
}

// Summary Discord notification types
export interface DiscordSummaryMessage {
  id: string;
  type: SummaryType;
  startDate: Date;
  endDate: Date;
  totalOrders: number;
  totalRevenue: number;
  totalProfit: number;
  totalDeliveryCharges: number;
  generatedBy: {
    name: string;
    email: string;
  };
}

// Function to get color based on summary type
function getSummaryTypeColor(type: SummaryType): number {
  switch (type) {
    case SummaryType.DAILY:
      return 0x3b82f6; // Blue
    case SummaryType.WEEKLY:
      return 0x10b981; // Green
    case SummaryType.MONTHLY:
      return 0xf59e0b; // Orange
    case SummaryType.YEARLY:
      return 0x8b5cf6; // Purple
    default:
      return 0x6b7280; // Gray
  }
}

// Function to format date range based on summary type
function formatSummaryDateRange(summary: DiscordSummaryMessage): string {
  const start = new Date(summary.startDate);
  const end = new Date(summary.endDate);

  if (summary.type === SummaryType.DAILY) {
    return start.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  }

  if (summary.type === SummaryType.WEEKLY) {
    return `${start.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    })} - ${end.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })}`;
  }

  if (summary.type === SummaryType.MONTHLY) {
    return start.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });
  }

  return start.getFullYear().toString();
}

export async function sendDiscordSummaryMessage(
  summary: DiscordSummaryMessage
) {
  // Get the profit margin percentage
  const profitMargin =
    summary.totalRevenue > 0
      ? ((summary.totalProfit / summary.totalRevenue) * 100).toFixed(1)
      : "0";

  // Customize your embed message
  const embed = {
    title: `📊 ${
      summary.type.charAt(0) + summary.type.slice(1).toLowerCase()
    } Summary Created`,
    color: getSummaryTypeColor(summary.type),
    fields: [
      { name: "Summary ID", value: summary.id, inline: true },
      { name: "Period", value: formatSummaryDateRange(summary), inline: true },
      { name: "Generated By", value: summary.generatedBy.name, inline: true },
      {
        name: "Total Revenue",
        value: `৳${summary.totalRevenue.toLocaleString("bn")}`,
        inline: true,
      },
      {
        name: "Total Profit",
        value: `৳${summary.totalProfit.toLocaleString(
          "bn"
        )} (${profitMargin}%)`,
        inline: true,
      },
      {
        name: "Total Orders",
        value: summary.totalOrders.toLocaleString("bn"),
        inline: true,
      },
      {
        name: "Delivery Charges",
        value: `৳${summary.totalDeliveryCharges.toLocaleString("bn")}`,
        inline: true,
      },
      {
        name: "Avg Order Value",
        value: `৳${(summary.totalRevenue / summary.totalOrders).toFixed(0)}`,
        inline: true,
      },
      {
        name: "Summary Type",
        value: `${summary.type} Summary`,
        inline: true,
      },
    ],
    timestamp: new Date().toISOString(),
    footer: {
      text: "Udoy Mart Financial Summary System",
    },
  };

  try {
    // Use the provided webhook URL for summaries
    await fetch(Env.DISCORD_SUMMARY_WEBHOOK_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        content: `🎉 আলহামদুলিল্লাহ! নতুন ${summary.type.toLowerCase()} সামারি তৈরি হয়েছে 📈`,
        embeds: [embed],
      }),
    });
  } catch (error) {
    console.error("Failed to send Discord summary notification:", error);
  }
}
