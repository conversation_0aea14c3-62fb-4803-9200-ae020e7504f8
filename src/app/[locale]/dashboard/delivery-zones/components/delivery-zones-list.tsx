"use client";

import { useState } from "react";
import { Prisma } from "@prisma/client";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@udoy/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  MapPin, 
  ChevronDown, 
  ChevronRight, 
  Plus 
} from "lucide-react";
import { EditZoneDialog } from "./edit-zone-dialog";
import { DeleteZoneDialog } from "./delete-zone-dialog";
import { CreateZoneDialog } from "./create-zone-dialog";

type DeliveryZoneWithRelations = Prisma.DeliveryZoneGetPayload<{
  include: {
    subZones: {
      include: {
        subZones: true;
        parentZone: true;
        _count: {
          select: {
            address: true;
          };
        };
      };
    };
    parentZone: true;
    _count: {
      select: {
        address: true;
      };
    };
  };
}>;

interface DeliveryZonesListProps {
  zones: DeliveryZoneWithRelations[];
}

export function DeliveryZonesList({ zones }: DeliveryZonesListProps) {
  const [editingZone, setEditingZone] = useState<DeliveryZoneWithRelations | null>(null);
  const [deletingZone, setDeletingZone] = useState<DeliveryZoneWithRelations | null>(null);
  const [expandedZones, setExpandedZones] = useState<Set<string>>(new Set());

  // Organize zones into parent-child hierarchy
  const parentZones = zones.filter(zone => !zone.parentId);

  const toggleZone = (zoneId: string) => {
    const newExpanded = new Set(expandedZones);
    if (newExpanded.has(zoneId)) {
      newExpanded.delete(zoneId);
    } else {
      newExpanded.add(zoneId);
    }
    setExpandedZones(newExpanded);
  };

  const renderZoneCard = (zone: DeliveryZoneWithRelations, level = 0) => {
    const hasSubZones = zone.subZones && zone.subZones.length > 0;
    const isExpanded = expandedZones.has(zone.id);
    const marginLeft = level * 24;
    
    return (
      <div key={zone.id} style={{ marginLeft: `${marginLeft}px` }}>
        <Card className="mb-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {hasSubZones && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleZone(zone.id)}
                    className="h-6 w-6 p-0"
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                )}
                {!hasSubZones && <div className="w-6" />}
                
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <CardTitle className="text-lg">{zone.name}</CardTitle>
                    {zone.nam && (
                      <p className="text-sm text-muted-foreground">({zone.nam})</p>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {hasSubZones && (
                  <CreateZoneDialog zones={zones} defaultParentId={zone.id}>
                    <Button variant="outline" size="sm">
                      <Plus className="mr-2 h-4 w-4" />
                      Add Sub-zone
                    </Button>
                  </CreateZoneDialog>
                )}
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setEditingZone(zone)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setDeletingZone(zone)}
                      className="text-destructive"
                      disabled={(zone._count?.address || 0) > 0 || (zone.subZones?.length || 0) > 0}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Slug:</span>
                <code className="ml-2 bg-muted px-2 py-1 rounded text-xs">
                  {zone.slug}
                </code>
              </div>
              
              <div>
                <span className="text-muted-foreground">Charges:</span>
                <div className="ml-2 flex items-center space-x-2">
                  <span>৳{zone.charge}</span>
                  {zone.express > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      Express: ৳{zone.express}
                    </Badge>
                  )}
                </div>
              </div>
              
              <div>
                <span className="text-muted-foreground">Type:</span>
                <Badge 
                  variant={zone.isBase ? "default" : "secondary"} 
                  className="ml-2 text-xs"
                >
                  {zone.isBase ? "Base Zone" : "Sub Zone"}
                </Badge>
              </div>
              
              <div>
                <span className="text-muted-foreground">Usage:</span>
                <div className="ml-2 flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    {zone._count?.address || 0} addresses
                  </Badge>
                  {hasSubZones && (
                    <Badge variant="outline" className="text-xs">
                      {zone.subZones.length} sub-zones
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Render sub-zones when expanded */}
        {hasSubZones && isExpanded && (
          <div className="ml-4">
            {zone.subZones.map(subZone => 
              renderZoneCard(subZone as DeliveryZoneWithRelations, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="space-y-4">
        {parentZones.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <MapPin className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No delivery zones found</h3>
              <p className="text-muted-foreground mb-4">
                Create your first delivery zone to get started.
              </p>
              <CreateZoneDialog zones={zones}>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create First Zone
                </Button>
              </CreateZoneDialog>
            </CardContent>
          </Card>
        ) : (
          parentZones.map(zone => renderZoneCard(zone))
        )}
      </div>

      {/* Edit Dialog */}
      {editingZone && (
        <EditZoneDialog
          zone={editingZone}
          zones={zones}
          isOpen={!!editingZone}
          onClose={() => setEditingZone(null)}
        />
      )}

      {/* Delete Dialog */}
      {deletingZone && (
        <DeleteZoneDialog
          zone={deletingZone}
          isOpen={!!deletingZone}
          onClose={() => setDeletingZone(null)}
        />
      )}
    </>
  );
}
