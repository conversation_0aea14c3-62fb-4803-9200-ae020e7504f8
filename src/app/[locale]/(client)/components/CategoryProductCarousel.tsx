/**
 * CategoryProductCarousel Component
 *
 * A server-side rendered carousel component that displays products from a given category slug.
 * It recursively fetches products from all child categories and randomizes them for better variety.
 *
 * Features:
 * - Server-side rendering with caching
 * - Recursive category traversal (fetches from all subcategories)
 * - Product randomization/mixing from different child categories
 * - Responsive design with customizable items per view
 * - Optional navigation controls
 * - Uses existing ProductItem component for consistent styling
 *
 * Usage:
 * ```tsx
 * <CategoryProductCarousel
 *   categorySlug="food"
 *   title="Food Items"
 *   maxProducts={15}
 *   showNavigation={true}
 *   itemsPerView={{
 *     mobile: 2,
 *     tablet: 3,
 *     desktop: 4,
 *     large: 6
 *   }}
 * />
 * ```
 */

import { getPrisma } from "@udoy/utils/db-utils";
import ProductItem from "./ProductItem";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { <PERSON>acheKey } from "@udoy/utils/cache-key";
import { getLocale } from "next-intl/server";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@udoy/components/ui/carousel";
import {
  Category,
  Product,
  ProductAvailability,
  ProductImage,
  QuantityUnit,
} from "@prisma/client";
import { cn } from "@udoy/utils/shadcn";
import Autoplay from "embla-carousel-autoplay";
import { CategoryWithSubcategories } from "@udoy/utils/types";
import Locale from "@udoy/components/Locale";
import Link from "next/link";
import CarouselCore from "./CarouselCore";

export type ProductWithRelations = Product & {
  images: ProductImage[];
  unit: QuantityUnit;
  category: { slug: string } | null;
  availability: ProductAvailability[];
};

interface CategoryProductCarouselProps {
  categorySlug: string;
  title?: string;
  maxProducts?: number;
  className?: string;
  showNavigation?: boolean;
  autoplay?: boolean;
  delay?: number;
}

async function getCategoryWithAllProducts(
  categorySlug: string
): Promise<{ products: ProductWithRelations[]; category: Category | null }> {
  "use cache";
  cacheTag(CacheKey.CategoryProducts(categorySlug));
  cacheTag(CacheKey.AllCategoryProducts());
  cacheLife("hours");

  const prisma = getPrisma();

  try {
    // First, get the category and check if it exists
    const category = await prisma.category.findUnique({
      where: { slug: categorySlug, hide: false },
      // select: { id: true, isBase: true },
    });

    if (!category) {
      console.warn(
        `Category with slug "${categorySlug}" not found or is hidden`
      );
      return { products: [], category: null };
    }

    // Function to recursively get all category IDs including subcategories
    const getAllCategoryIds = async (categoryId: string): Promise<string[]> => {
      const categoryIds = [categoryId];

      // Get all subcategories recursively
      const subcategories = await prisma.category.findMany({
        where: {
          parentId: categoryId,
          hide: false,
        },
        select: { id: true },
      });

      // Recursively get subcategories of subcategories
      for (const subcategory of subcategories) {
        const subIds = await getAllCategoryIds(subcategory.id);
        categoryIds.push(...subIds);
      }

      return categoryIds;
    };

    // Get all category IDs (including nested subcategories)
    const allCategoryIds = await getAllCategoryIds(category.id);

    // Fetch all products from these categories
    const products = await prisma.product.findMany({
      where: {
        categoryId: {
          in: allCategoryIds,
        },
        hide: false,
        category: {
          hide: false,
        },
      },
      include: {
        images: true,
        unit: true,
        category: { select: { slug: true } },
        availability: {
          orderBy: {
            priority: "desc",
          },
        },
      },
      orderBy: [{ featured: "desc" }, { position: "asc" }],
    });

    return { products, category: category || null };
  } catch (error) {
    console.error(
      `Error fetching products for category "${categorySlug}":`,
      error
    );
    return { products: [], category: null };
  }
}

function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

async function CategoryProductCarousel({
  categorySlug,
  maxProducts = 12,
  className = "",
  showNavigation = true,
  autoplay = false,
  delay = 2000,
}: CategoryProductCarouselProps) {
  const { products, category } = await getCategoryWithAllProducts(categorySlug);
  const locale = await getLocale();
  if (products.length === 0) {
    return null;
  }

  // Shuffle products to mix different child categories
  const shuffledProducts = shuffleArray(products);

  // Limit the number of products and filter out products without images
  const limitedProducts = shuffledProducts
    .filter(
      (product) =>
        product.images && product.images.length > 0 && product.images[0]?.url
    )
    .slice(0, maxProducts);

  // Use fixed responsive classes similar to PopularProducts with consistent height

  return (
    <CarouselCore
      category={category}
      products={limitedProducts}
      className={className}
      showNavigation={showNavigation}
      autoplay={autoplay}
      delay={delay}
      locale={locale}
    />
  );
}

export default CategoryProductCarousel;
