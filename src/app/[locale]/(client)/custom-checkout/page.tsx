import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import CartPreview from "./components/CartPreview";
import DeliveryAddress from "./components/DeliveryAddress";
import Locale from "@udoy/components/Locale";
import TotalPricing from "./components/TotalPricing";
import { Address } from "@prisma/client";
import TargetCustomer from "./components/TargetCustomer";

async function getData() {
  const prisma = getPrisma();
  const userId = await CookieUtil.userId();

  let addresses = [] as Address[];

  if (userId) {
    addresses = await prisma.address.findMany({
      where: {
        userId,
      },
      include: {
        zone: true,
      },
    });
  }

  const zones = await prisma.deliveryZone.findMany({
    where: {
      parentZone: {
        slug: "mollahat",
      },
    },
    include: {
      subZones: true,
    },
  });

  return {
    addresses: addresses.reverse(),
    zones,
  };
}

async function CheckoutPage() {
  const { zones, addresses } = await getData();

  return (
    <main
      className="md:px-4 px-2 pb-16 overflow-y-auto"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <h1 className="text-4xl font-bold text-center mt-4">
        <Locale bn="কাস্টম চেকআউট">Custom Checkout</Locale>
      </h1>
      {/* <CheckoutForm
        addresses={addresses}
        zones={zones || []}
        subtotal={subtotal || 0}
        currentAddress={currentAddress}
      /> */}
      <div className="max-w-4xl mx-auto md:grid-cols-3 sm:grid gap-4 mt-6 grid-cols-1 flex flex-col-reverse ">
        <div className="md:col-span-2 col-span-1 ">
          <CartPreview />
        </div>
        <div className="col-span-1 flex flex-col gap-4">
          <TargetCustomer  />
          <DeliveryAddress zones={zones || []} />
          <TotalPricing />
        </div>
      </div>
    </main>
  );
}

export default CheckoutPage;
