"use server";

import { OrderStatus, Role } from "@prisma/client";
import {
  notifyUsers,
  sendPushNotification,
} from "@udoy/libs/backend/push-service";
import { sendDiscordOrderMessage } from "@udoy/libs/backend/discord";
import { auditStock } from "@udoy/libs/backend/orders";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { cookies } from "next/headers";
import { Env } from "@udoy/libs/env";

export async function manageCart(productId: string, action: "add" | "remove") {
  try {
    const userId = await CookieUtil.userId();
    let cookieCartId = await CookieUtil.cartId();

    const prisma = getPrisma();

    // Get product information to check minimum order requirements
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { minOrder: true, maxOrder: true, supply: true },
    });

    if (!product) {
      return ActionError("Product not found");
    }

    let cart =
      cookieCartId &&
      (await prisma.cart.findUnique({
        where: { id: cookieCartId },
        include: {
          user: true,
        },
      }));

    if (!cart) {
      if (userId) {
        const user = await prisma.user.findUnique({
          where: { id: userId },
          include: {
            cart: {
              include: {
                user: true,
              },
            },
          },
        });

        cart = user?.cart!;
      }
    }

    if (!cart) {
      const data = userId !== null ? { userId } : {};
      cart = await prisma.cart.create({
        data,
        include: {
          user: true,
        },
      });
    }

    const cartId = cart.id;

    if (cartId !== cookieCartId) {
      (await cookies()).set("cartId", cartId, {
        maxAge: 60 * 60 * 24 * 30,
      });
    }

    // Get current cart item to check existing quantity
    const existingCartItem = await prisma.cartItem.findUnique({
      where: {
        cartId_productId: {
          cartId,
          productId,
        },
      },
    });

    let newQuantity: number;

    if (action === "add") {
      if (existingCartItem) {
        newQuantity = existingCartItem.quantity + 1;
      } else {
        // For new items, add minimum order quantity
        newQuantity = product.minOrder;
      }
    } else {
      if (existingCartItem) {
        newQuantity = Math.max(existingCartItem.quantity - 1, 0);
        // Don't allow reducing below minimum order (except to 0 for removal)
        if (newQuantity > 0 && newQuantity < product.minOrder) {
          newQuantity = 0; // Remove completely if below minimum
        }
      } else {
        newQuantity = 0;
      }
    }

    if (newQuantity === 0) {
      // Remove item completely
      if (existingCartItem) {
        await prisma.cartItem.delete({
          where: { id: existingCartItem.id },
        });
      }
    } else {
      // Update or create cart item
      await prisma.cartItem.upsert({
        where: {
          cartId_productId: {
            cartId,
            productId,
          },
        },
        create: {
          quantity: newQuantity,
          productId,
          cartId,
        },
        update: {
          quantity: newQuantity,
        },
      });
    }

    return true;
  } catch (error) {
    console.log(error);
    return ActionError("Failed to add to cart");
  }
}

export async function updateCurrentAddress(addressId: string) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const address = await prisma.address.findUnique({
      where: {
        userId,
        id: addressId,
      },
    });

    if (!address) {
      return ActionError("Invalid Address");
    }

    await prisma.cart.update({
      where: {
        userId,
      },
      data: {
        addressId,
      },
    });

    return true;
  } catch (error) {
    return ActionError("Failed to add to cart");
  }
}

export async function placeOrder(customerId?: number) {
  try {
    let userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login To Confirm Order");
    }

    if (customerId) {
      const admin = await prisma.user.findUnique({
        where: {
          id: userId,
          role: {
            in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
          },
        },
        include: {
          cart: true,
        },
      });

      if (!admin) {
        return ActionError("Unauthorized");
      }

      const customerCart = await prisma.cart.findUnique({
        where: {
          userId: customerId,
        },
      });

      if (!customerCart) {
        return ActionError("Customer Cart Not Found");
      }

      // Clear customer's existing cart items before adding admin's items
      await prisma.cartItem.deleteMany({
        where: {
          cartId: customerCart.id,
        },
      });

      await prisma.cartItem.updateMany({
        where: {
          cartId: admin?.cart?.id!,
        },
        data: {
          cartId: customerCart.id,
        },
      });

      userId = customerId;
    }

    const cart = await getPrisma().cart.findUnique({
      where: {
        userId,
      },
      include: {
        user: true,
        items: {
          include: {
            product: true,
          },
        },
        address: {
          include: {
            zone: true,
          },
        },
      },
    });

    if (cart?.user?.blocked) {
      return ActionError("Your account has been blocked");
    }

    if (!cart) {
      return ActionError("Cart Not Found");
    }

    if (!cart.addressId) {
      return ActionError("Please Select Address");
    }

    if (!cart.address?.zone) {
      return ActionError("Please Select Delivary Zone");
    }

    if (cart.items.length === 0) {
      return ActionError("Cart is Empty");
    }

    const { inStock, stockChange } = auditStock(cart.items);

    if (!inStock) {
      return ActionError("Cart Contains Out Of Stock Product");
    }

    // Validate minimum order requirements
    const invalidMinOrderItems = cart.items.filter(
      (item) => item.quantity < (item.product as any).minOrder
    );

    if (invalidMinOrderItems.length > 0) {
      const itemNames = invalidMinOrderItems
        .map((item) => item.product.name)
        .join(", ");
      return ActionError(
        `Minimum order requirements not met for: ${itemNames}`
      );
    }

    let subTotal = 0;
    let totalDiscount = 0;
    let deliveryCharge = cart.address.zone.charge;

    if (!cart.address.zone.isBase) {
      return ActionError("Invalid Delivery Zone");
    }

    let totalProfit = 0;
    const orderItems = cart.items.map((item) => {
      subTotal += item.product.price * item.quantity;
      totalDiscount += item.product.discount * item.quantity;

      // Calculate profit for this item
      const finalPrice = item.product.price - item.product.discount;
      const itemProfit =
        (finalPrice - item.product.sourcePrice) * item.quantity;
      totalProfit += itemProfit;

      return {
        price: finalPrice,
        quantity: item.quantity,
        productId: item.productId,
        sourcePrice: item.product.sourcePrice, // Add source price to order item
      };
    });

    const createOrder = prisma.order.create({
      data: {
        subTotal: subTotal - totalDiscount,
        discount: totalDiscount,
        shipping: deliveryCharge,
        profit: totalProfit, // Add calculated profit to order
        buyerId: userId,
        addressId: cart.addressId,
        timeline: {
          create: {
            status: OrderStatus.PENDING,
            note: "Order Placed, Not Confirmed Yet",
          },
        },
        orderItems: {
          createMany: {
            data: orderItems,
          },
        },
      },

      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        address: {
          include: {
            zone: true,
          },
        },
        buyer: true,
      },
    });

    const deleteCartItems = prisma.cartItem.deleteMany({
      where: {
        cartId: cart.id,
      },
    });

    const updateStocks = stockChange.map((item) =>
      prisma.product.update({
        where: { id: item.productId },
        data: {
          supply: {
            decrement: item.decrement,
          },
        },
      })
    );

    const [order] = await prisma.$transaction([
      createOrder,
      deleteCartItems,
      ...updateStocks,
    ]);

    sendDiscordOrderMessage(order);
    const admins = await prisma.user.findMany({
      where: {
        role: {
          in: [
            Role.ADMIN,
            Role.SUPER_ADMIN,
            Role.MAINTAINER,
            Role.DELIVERY_MAN,
          ],
        },
      },
    });

    await notifyUsers(
      admins.map((admin) => admin.id),
      {
        title: "নতুন অর্ডার এসেছে!",
        body: `${cart.address.name}, ${(
          order.subTotal + order.shipping
        ).toLocaleString("bn")} টাকার একটি অর্ডার করেছেন। অর্ডারটি যাবে ${
          cart.address.zone.name
        }`,
        targetUrl: `${Env.NEXT_PUBLIC_FRONTEND_URL}/deliver/unassigned?order=${order.id}`,
      }
    );

    return order;
  } catch (error) {
    console.log(error);
    return ActionError("Failed to place order");
  }
}
