"use client";

import { useState, useEffect } from "react";
import { Prisma } from "@prisma/client";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import { Checkbox } from "@udoy/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { updateDeliveryZone } from "../actions";
import { toast } from "sonner";
import { isActionError } from "@udoy/utils/app-error";

type DeliveryZoneWithRelations = Prisma.DeliveryZoneGetPayload<{
  include: {
    subZones: true;
    parentZone: true;
    _count: {
      select: {
        address: true;
      };
    };
  };
}>;

interface EditZoneDialogProps {
  zone: DeliveryZoneWithRelations;
  zones: DeliveryZoneWithRelations[];
  isOpen: boolean;
  onClose: () => void;
}

export function EditZoneDialog({ zone, zones, isOpen, onClose }: EditZoneDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    nam: "",
    slug: "",
    charge: "",
    express: "",
    isBase: false,
    parentId: "",
  });

  useEffect(() => {
    if (zone) {
      setFormData({
        name: zone.name,
        nam: zone.nam || "",
        slug: zone.slug,
        charge: zone.charge.toString(),
        express: zone.express.toString(),
        isBase: zone.isBase,
        parentId: zone.parentId || "",
      });
    }
  }, [zone]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.slug) {
      toast.error("Please fill in required fields");
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateDeliveryZone(zone.id, {
        name: formData.name,
        nam: formData.nam || undefined,
        slug: formData.slug,
        charge: parseInt(formData.charge) || 0,
        express: parseInt(formData.express) || 0,
        isBase: formData.isBase,
        parentId: formData.parentId || undefined,
      });

      if (isActionError(result)) {
        toast.error(result.error.message);
      } else {
        toast.success("Delivery zone updated successfully");
        onClose();
        // The page will be revalidated by the server action
        window.location.reload();
      }
    } catch (error) {
      toast.error("Failed to update delivery zone");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Get available parent zones (exclude current zone and its descendants)
  const getDescendantIds = (zoneId: string): string[] => {
    const descendants: string[] = [];
    const findDescendants = (id: string) => {
      descendants.push(id);
      zones.filter(z => z.parentId === id).forEach(child => {
        findDescendants(child.id);
      });
    };
    findDescendants(zoneId);
    return descendants;
  };

  const excludedIds = getDescendantIds(zone.id);
  const availableParentZones = zones.filter(z => 
    !z.parentId && !excludedIds.includes(z.id)
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Delivery Zone</DialogTitle>
          <DialogDescription>
            Update the delivery zone information and charges.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Zone Name (English) *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., Dhaka City"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="nam">Zone Name (Bangla)</Label>
                <Input
                  id="nam"
                  value={formData.nam}
                  onChange={(e) => handleInputChange("nam", e.target.value)}
                  placeholder="e.g., ঢাকা শহর"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => handleInputChange("slug", e.target.value)}
                placeholder="e.g., dhaka-city"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="charge">Regular Charge (৳)</Label>
                <Input
                  id="charge"
                  type="number"
                  min="0"
                  value={formData.charge}
                  onChange={(e) => handleInputChange("charge", e.target.value)}
                  placeholder="0"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="express">Express Charge (৳)</Label>
                <Input
                  id="express"
                  type="number"
                  min="0"
                  value={formData.express}
                  onChange={(e) => handleInputChange("express", e.target.value)}
                  placeholder="0"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="parentId">Parent Zone</Label>
              <Select
                value={formData.parentId}
                onValueChange={(value) => handleInputChange("parentId", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select parent zone (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No parent (top-level zone)</SelectItem>
                  {availableParentZones.map((parentZone) => (
                    <SelectItem key={parentZone.id} value={parentZone.id}>
                      {parentZone.name} {parentZone.nam && `(${parentZone.nam})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isBase"
                checked={formData.isBase}
                onCheckedChange={(checked) => handleInputChange("isBase", !!checked)}
              />
              <Label htmlFor="isBase">Base Zone</Label>
            </div>

            {zone._count.address > 0 && (
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm text-muted-foreground">
                  ⚠️ This zone is currently used by {zone._count.address} address(es).
                  Changes may affect existing orders and deliveries.
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Updating..." : "Update Zone"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
