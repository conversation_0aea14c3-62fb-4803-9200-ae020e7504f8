"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { productFormSchema } from "../utils";
import { z } from "zod";
import { FileManager } from "@udoy/libs/backend/image-util";
import { createId } from "@paralleldrive/cuid2";
import { zImage } from "@udoy/utils/zod";
import { extname } from "path";

export async function createProduct(
  input: z.input<typeof productFormSchema>,
  form: FormData
) {
  try {
    const userId = await CookieUtil.userId();
    const data = productFormSchema.parse(input);

    if (!userId) {
      return ActionError("Login to Continue");
    }
    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const product = await prisma.product.create({
      data: {
        name: data.name,
        nam: data.nam,
        slug: data.slug,
        description: data.description,
        biboron: data.biboron,
        titleBe: data.titleBe,

        price: data.price,
        sourcePrice: data.sourcePrice,
        discount: data.discount,

        supply: data.supply,
        amount: data.amount,
        quantity: data.quantity,

        categoryId: data.categoryId,
        unitId: data.unitId,
        shopId: data.shopId,
        companyId: data.companyId,
        extraCharge: data.extraCharge,

        minOrder: data.minOrder,
        maxOrder: data.maxOrder,
        hide: data.hide,
        alwaysAvailable: data.alwaysAvailable,
        isHomeMade: data.isHomeMade,
        isHigherPriced: data.isHigherPriced,

        position: data.position,
        featured: data.featured,
      },
      include: {
        category: true,
      },
    });

    const images = z.array(zImage).parse(form.getAll("images"));

    const imageUrls = await Promise.all(
      images.map(async (image) => {
        const cuid = createId();
        const fileExt = extname(image.name);
        const newFile = new File([image], `${cuid}${fileExt}`, {
          type: image.type,
        });
        const url = await FileManager.imageUpload(newFile, `products`);

        return { url, id: cuid };
      })
    );

    await prisma.product.update({
      where: { id: product.id },
      data: {
        images: { createMany: { data: imageUrls } },
      },
    });

    revalidatePath(`/${product.category?.slug}`);

    return product;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Create Product");
  }
}
