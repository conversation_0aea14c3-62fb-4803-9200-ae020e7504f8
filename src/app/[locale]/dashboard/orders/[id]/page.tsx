import { <PERSON><PERSON><PERSON><PERSON>, Printer } from "lucide-react";
import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Badge } from "@udoy/components/ui/badge";
import { Separator } from "@udoy/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { cn } from "@udoy/utils/shadcn";
import { OrderTimeline } from "../../components/order-timeline";
import { OrderStatus } from "@prisma/client";
import { getPrisma } from "@udoy/utils/db-utils";
import { notFound } from "next/navigation";
import Link from "next/link";
import { StatusUpdateForm } from "../../components/status-update-form";
import { OrderDetails } from "../components/OrderDetails";

// Helper function to get status badge variant
function getStatusBadgeVariant(status: OrderStatus) {
  switch (status) {
    case OrderStatus.DELIVERED:
      return "default";
    case OrderStatus.CONFIRMED:
      return "secondary";
    case OrderStatus.PENDING:
      return "outline";
    case OrderStatus.CANCELLED:
      return "destructive";
    case OrderStatus.SHIPPING:
      return "warning";
    case OrderStatus.RETURNED:
      return "destructive";
    default:
      return "outline";
  }
}

// Helper function to format currency
function formatCurrency(amount: number) {
  return `৳ ${amount.toLocaleString()}`;
}

// Helper function to format date
function formatDate(dateString: string | Date) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

async function getOrderDetails(orderId: string) {
  const order = await getPrisma().order.findUnique({
    where: {
      id: parseInt(orderId),
    },
    include: {
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
              unit: true,
            },
          },
          picker: true,
        },
        orderBy: {
          id: "desc",
        },
      },
      address: {
        include: {
          zone: true,
        },
      },
      buyer: true,
      timeline: {
        orderBy: {
          createdAt: "desc",
        },
      },
      deliveryMan: true,
    },
  });

  return order;
}

export default async function OrderDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: orderId } = await params;
  const order = await getOrderDetails(orderId);

  if (!order) {
    return notFound();
  }

  return <OrderDetails order={order} />;

  // return (
  //   <div className="flex flex-col gap-6">
  //     <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
  //       <div className="flex items-center gap-4">
  //         <Button variant="outline" size="icon" asChild>
  //           <Link href="/dashboard/orders">
  //             <ArrowLeft className="h-4 w-4" />
  //           </Link>
  //         </Button>
  //         <div>
  //           <h1 className="text-3xl font-bold tracking-tight">
  //             Order #{order.id}
  //           </h1>
  //           <p className="text-muted-foreground">
  //             Placed on {formatDate(order.createdAt)}
  //           </p>
  //         </div>
  //       </div>
  //       <div className="flex flex-wrap items-center gap-2">
  //         <Button asChild>
  //           <Link href={`/api/orders/${order.id}/invoice`} target="_blank">
  //             View Invoice
  //           </Link>
  //         </Button>
  //       </div>
  //     </div>

  //     {/* Order Status and Summary */}
  //     <div className="grid gap-6 md:grid-cols-3">
  //       <Card className="md:col-span-2">
  //         <CardHeader className="flex flex-row items-center justify-between">
  //           <div>
  //             <CardTitle>Order Status</CardTitle>
  //             <CardDescription>Current status and history</CardDescription>
  //           </div>
  //           <Badge
  //             variant={getStatusBadgeVariant(order.status) as any}
  //             className={cn(
  //               "text-sm py-1 px-3",
  //               order.status === OrderStatus.SHIPPING &&
  //                 "bg-yellow-500 hover:bg-yellow-600"
  //             )}
  //           >
  //             {order.status.replace("_", " ")}
  //           </Badge>
  //         </CardHeader>
  //         <CardContent>
  //           <OrderTimeline timeline={order.timeline} />
  //         </CardContent>
  //         <CardFooter className="flex justify-between border-t pt-6">
  //           <div className="text-sm text-muted-foreground">
  //             Update the order status
  //           </div>
  //           <StatusUpdateForm orderId={order.id} currentStatus={order.status} />
  //         </CardFooter>
  //       </Card>

  //       <Card>
  //         <CardHeader>
  //           <CardTitle>Order Summary</CardTitle>
  //           <CardDescription>Order details and totals</CardDescription>
  //         </CardHeader>
  //         <CardContent className="space-y-4">
  //           <div className="flex justify-between">
  //             <span className="text-muted-foreground">Subtotal</span>
  //             <span>{formatCurrency(order.subTotal)}</span>
  //           </div>
  //           <div className="flex justify-between">
  //             <span className="text-muted-foreground">Shipping</span>
  //             <span>{formatCurrency(order.shipping)}</span>
  //           </div>
  //           <Separator />
  //           <div className="flex justify-between font-medium">
  //             <span>Total</span>
  //             <span>{formatCurrency(order.subTotal + order.shipping)}</span>
  //           </div>
  //           <Separator />
  //           <div className="space-y-1">
  //             <div className="text-sm font-medium">Payment Information</div>
  //             <div className="flex justify-between text-sm">
  //               <span className="text-muted-foreground">Method</span>
  //               <span>{"Cash On Delivery"}</span>
  //             </div>
  //             {/* {order.paymentId && (
  //               <div className="flex justify-between text-sm">
  //                 <span className="text-muted-foreground">Transaction ID</span>
  //                 <span>{order.paymentId}</span>
  //               </div>
  //             )} */}
  //           </div>
  //         </CardContent>
  //       </Card>
  //     </div>

  //     {/* Order Details Tabs */}
  //     <Tabs defaultValue="items">
  //       <TabsList>
  //         <TabsTrigger value="items">Order Items</TabsTrigger>
  //         <TabsTrigger value="customer">Customer</TabsTrigger>
  //         <TabsTrigger value="shipping">Shipping</TabsTrigger>
  //       </TabsList>
  //       <TabsContent value="items" className="mt-4">
  //         <Card>
  //           <CardHeader>
  //             <CardTitle>Order Items</CardTitle>
  //             <CardDescription>Items included in this order</CardDescription>
  //           </CardHeader>
  //           <CardContent>
  //             <Table>
  //               <TableHeader>
  //                 <TableRow>
  //                   <TableHead>Product</TableHead>
  //                   <TableHead>Quantity</TableHead>
  //                   <TableHead className="text-right">Unit Price</TableHead>
  //                   <TableHead className="text-right">Total</TableHead>
  //                 </TableRow>
  //               </TableHeader>
  //               <TableBody>
  //                 {order.orderItems.map((item) => (
  //                   <TableRow key={item.id}>
  //                     <TableCell>
  //                       <div className="flex items-center gap-3">
  //                         {item.product.images && item.product.images[0] ? (
  //                           <img
  //                             src={item.product.images[0].url}
  //                             alt={item.product.name}
  //                             className="h-10 w-10 rounded-md object-cover"
  //                           />
  //                         ) : (
  //                           <div className="h-10 w-10 rounded-md bg-muted"></div>
  //                         )}
  //                         <div>
  //                           <p className="font-medium">{item.product.name}</p>
  //                           <p className="text-xs text-muted-foreground">
  //                             ID: {item.product.id}
  //                           </p>
  //                         </div>
  //                       </div>
  //                     </TableCell>
  //                     <TableCell>{item.quantity}</TableCell>
  //                     <TableCell className="text-right">
  //                       {formatCurrency(item.price)}
  //                     </TableCell>
  //                     <TableCell className="text-right">
  //                       {formatCurrency(item.price * item.quantity)}
  //                     </TableCell>
  //                   </TableRow>
  //                 ))}
  //               </TableBody>
  //             </Table>
  //           </CardContent>
  //         </Card>
  //       </TabsContent>
  //       <TabsContent value="customer" className="mt-4">
  //         <Card>
  //           <CardHeader>
  //             <CardTitle>Customer Information</CardTitle>
  //             <CardDescription>
  //               Details about the customer who placed this order
  //             </CardDescription>
  //           </CardHeader>
  //           <CardContent className="space-y-4">
  //             <div className="grid gap-4 md:grid-cols-2">
  //               <div>
  //                 <h4 className="mb-2 text-sm font-medium">
  //                   Contact Information
  //                 </h4>
  //                 <div className="rounded-md border p-4">
  //                   <p className="font-medium">{order.buyer.name}</p>
  //                   <p>{order.buyer.email}</p>
  //                   <p>{order.address.phone}</p>
  //                 </div>
  //               </div>
  //               <div>
  //                 <h4 className="mb-2 text-sm font-medium">Billing Address</h4>
  //                 <div className="rounded-md border p-4">
  //                   <p className="font-medium">{order.address.name}</p>
  //                   <p>{order.address.home}</p>
  //                   <p>{order.address.zone.name}</p>
  //                   <p>{order.address.phone}</p>
  //                   {order.address.location && (
  //                     <p className="text-muted-foreground">
  //                       {order.address.location}
  //                     </p>
  //                   )}
  //                 </div>
  //               </div>
  //             </div>
  //             <div className="flex justify-end">
  //               <Button variant="outline" asChild>
  //                 <Link href={`/dashboard/customers/${order.buyerId}`}>
  //                   View Customer Profile
  //                 </Link>
  //               </Button>
  //             </div>
  //           </CardContent>
  //         </Card>
  //       </TabsContent>
  //       <TabsContent value="shipping" className="mt-4">
  //         <Card>
  //           <CardHeader>
  //             <CardTitle>Shipping Information</CardTitle>
  //             <CardDescription>
  //               Delivery details and shipping address
  //             </CardDescription>
  //           </CardHeader>
  //           <CardContent className="space-y-4">
  //             <div className="grid gap-4 md:grid-cols-2">
  //               <div>
  //                 <h4 className="mb-2 text-sm font-medium">Shipping Address</h4>
  //                 <div className="rounded-md border p-4">
  //                   <p className="font-medium">{order.address.name}</p>
  //                   <p>{order.address.home}</p>
  //                   <p>{order.address.zone.name}</p>
  //                   <p>{order.address.phone}</p>
  //                   {order.address.location && (
  //                     <p className="text-muted-foreground">
  //                       {order.address.location}
  //                     </p>
  //                   )}
  //                 </div>
  //               </div>
  //               <div>
  //                 <h4 className="mb-2 text-sm font-medium">Shipping Method</h4>
  //                 <div className="rounded-md border p-4">
  //                   <p className="font-medium">Standard Shipping</p>
  //                   <p className="text-muted-foreground">
  //                     Estimated delivery: 2-3 business days
  //                   </p>
  //                   <p className="mt-2">{formatCurrency(order.shipping)}</p>
  //                 </div>
  //               </div>
  //             </div>
  //           </CardContent>
  //         </Card>
  //       </TabsContent>
  //     </Tabs>
  //   </div>
  // );
}
