"use client";

import { useState } from "react";
import { Prisma } from "@prisma/client";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import { Checkbox } from "@udoy/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { createDeliveryZone } from "../actions";
import { toast } from "sonner";
import { isActionError } from "@udoy/utils/app-error";

type DeliveryZoneWithRelations = Prisma.DeliveryZoneGetPayload<{
  include: {
    subZones: true;
    parentZone: true;
    _count: {
      select: {
        address: true;
      };
    };
  };
}>;

interface CreateZoneDialogProps {
  children: React.ReactNode;
  zones: DeliveryZoneWithRelations[];
  defaultParentId?: string;
}

export function CreateZoneDialog({ children, zones, defaultParentId }: CreateZoneDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    nam: "",
    slug: "",
    charge: "",
    express: "",
    isBase: false,
    parentId: defaultParentId || "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.slug) {
      toast.error("Please fill in required fields");
      return;
    }

    setIsLoading(true);

    try {
      const result = await createDeliveryZone({
        name: formData.name,
        nam: formData.nam || undefined,
        slug: formData.slug,
        charge: parseInt(formData.charge) || 0,
        express: parseInt(formData.express) || 0,
        isBase: formData.isBase,
        parentId: formData.parentId || undefined,
      });

      if (isActionError(result)) {
        toast.error(result.error.message);
      } else {
        toast.success("Delivery zone created successfully");
        setIsOpen(false);
        setFormData({
          name: "",
          nam: "",
          slug: "",
          charge: "",
          express: "",
          isBase: false,
          parentId: "",
        });
        // The page will be revalidated by the server action
        window.location.reload();
      }
    } catch (error) {
      toast.error("Failed to create delivery zone");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-generate slug from name
    if (field === "name" && typeof value === "string") {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  // Get available parent zones (only zones without parents can be parents)
  const availableParentZones = zones.filter(zone => !zone.parentId);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Delivery Zone</DialogTitle>
          <DialogDescription>
            Add a new delivery zone with charges and configuration.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Zone Name (English) *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., Dhaka City"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="nam">Zone Name (Bangla)</Label>
                <Input
                  id="nam"
                  value={formData.nam}
                  onChange={(e) => handleInputChange("nam", e.target.value)}
                  placeholder="e.g., ঢাকা শহর"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => handleInputChange("slug", e.target.value)}
                placeholder="e.g., dhaka-city"
                required
              />
              <p className="text-xs text-muted-foreground">
                URL-friendly identifier (auto-generated from name)
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="charge">Regular Charge (৳)</Label>
                <Input
                  id="charge"
                  type="number"
                  min="0"
                  value={formData.charge}
                  onChange={(e) => handleInputChange("charge", e.target.value)}
                  placeholder="0"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="express">Express Charge (৳)</Label>
                <Input
                  id="express"
                  type="number"
                  min="0"
                  value={formData.express}
                  onChange={(e) => handleInputChange("express", e.target.value)}
                  placeholder="0"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="parentId">Parent Zone</Label>
              <Select
                value={formData.parentId}
                onValueChange={(value) => handleInputChange("parentId", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select parent zone (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No parent (top-level zone)</SelectItem>
                  {availableParentZones.map((zone) => (
                    <SelectItem key={zone.id} value={zone.id}>
                      {zone.name} {zone.nam && `(${zone.nam})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isBase"
                checked={formData.isBase}
                onCheckedChange={(checked) => handleInputChange("isBase", !!checked)}
              />
              <Label htmlFor="isBase">Base Zone</Label>
              <p className="text-xs text-muted-foreground">
                Mark as base zone if this is a primary delivery area
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Zone"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
