"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { z } from "zod";

export async function toggleUserBlock({
  userId,
  blocked,
}: {
  userId: number;
  blocked: boolean;
}) {
  try {
    const currentUserId = await CookieUtil.userId();

    if (!currentUserId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    // Check if current user has permission to block users
    const admin = await prisma.user.findUnique({
      where: {
        id: currentUserId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Prevent blocking super admins
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, name: true },
    });

    if (!targetUser) {
      return ActionError("User not found");
    }

    if (targetUser.role === Role.SUPER_ADMIN) {
      return ActionError("Cannot block super admin users");
    }

    // Prevent users from blocking themselves
    if (currentUserId === userId) {
      return ActionError("Cannot block yourself");
    }

    // Update user blocked status
    await prisma.user.update({
      where: { id: userId },
      data: { blocked },
    });

    // Revalidate the customer page to reflect changes
    revalidatePath(`/dashboard/customers/${userId}`);
    revalidatePath("/dashboard/customers");

    return {
      success: true,
      message: `User ${blocked ? "blocked" : "unblocked"} successfully`,
    };
  } catch (error) {
    console.error("Failed to toggle user block status:", error);
    return ActionError("Failed to update user block status");
  }
}

// Schema for balance update validation
const balanceUpdateSchema = z.object({
  userId: z.number().int().positive(),
  amount: z.number(),
  reason: z.string().min(1, "Reason is required").max(255, "Reason too long"),
});

export type BalanceUpdateInput = z.infer<typeof balanceUpdateSchema>;

export async function updateCustomerBalance(data: BalanceUpdateInput) {
  try {
    const currentUserId = await CookieUtil.userId();

    if (!currentUserId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    // Check if current user has permission to update customer balance
    const admin = await prisma.user.findUnique({
      where: {
        id: currentUserId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Validate input data
    const { userId, amount, reason } = balanceUpdateSchema.parse(data);

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true, balance: true },
    });

    if (!targetUser) {
      return ActionError("Customer not found");
    }

    // Update customer balance
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { balance: amount },
      select: { id: true, name: true, balance: true },
    });

    // Revalidate the customer page to reflect changes
    revalidatePath(`/dashboard/customers/${userId}`);
    revalidatePath("/dashboard/customers");

    return {
      success: true,
      message: `Customer balance updated successfully`,
      user: updatedUser,
    };
  } catch (error) {
    console.error("Failed to update customer balance:", error);
    if (error instanceof z.ZodError) {
      return ActionError(error.errors[0].message);
    }
    return ActionError("Failed to update customer balance");
  }
}
