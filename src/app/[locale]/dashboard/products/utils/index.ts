import { z } from "zod";

// Define the product form schema with Zod
export const productFormSchema = z.object({
  id: z.string().optional(),
  name: z
    .string()
    .min(3, { message: "Product name must be at least 3 characters" }),
  nam: z
    .string()
    .min(3, { message: "Product name must be at least 3 characters" }),
  slug: z
    .string()
    .min(3, { message: "Product slug must be at least 3 characters" })
    .regex(/^[a-z0-9-]+$/, { message: "Slug can only contain lowercase letters, numbers, and hyphens" }),
  description: z
    .string()
    .min(3, { message: "Description must be at least 3 characters" }),
  biboron: z
    .string()
    .min(3, { message: "Description must be at least 3 characters" }),
  titleBe: z.string().optional(),
  images: z.array(z.any()).optional(),

  categoryId: z.string().optional(),
  unitId: z.string(),
  shopId: z
    .string()
    .nullable()
    .transform((val) => (val === "" ? null : val)),
  companyId: z
    .string()
    .transform((val) => (val === "" ? null : val))
    .nullish(),

  amount: z.coerce.number().positive({ message: "Amount must be positive" }),
  quantity: z.coerce
    .number()
    .nonnegative({ message: "Quantity must be non-negative" }),
  minOrder: z.coerce
    .number()
    .min(1, { message: "Minimum order must be at least 1" }),
  maxOrder: z.coerce
    .number()
    .min(1, { message: "Maximum order must be at least 1" }),
  supply: z.coerce
    .number()
    .nonnegative({ message: "Supply must be non-negative" }),

  price: z.coerce
    .number()
    .nonnegative({ message: "Price must be non-negative" }),
  discount: z.coerce
    .number()
    .nonnegative({ message: "Discount must be non-negative" }),
  extraCharge: z.coerce
    .number()
    .nonnegative({ message: "Discount must be non-negative" }),
  sourcePrice: z.coerce
    .number()
    .nonnegative({ message: "Source price must be non-negative" }),
  hide: z.boolean().optional(),
  removedImages: z.array(z.string()),
  position: z.coerce.number().nonnegative(),
  featured: z.boolean(),
  alwaysAvailable: z.boolean().optional(),
  isHomeMade: z.boolean().optional(),
  isHigherPriced: z.boolean().optional(),
}).refine(data => data.minOrder <= data.maxOrder, {
  message: "Minimum order cannot be greater than maximum order",
  path: ["minOrder"]
});

export type ProductFormValues = z.infer<typeof productFormSchema>;
