import { OrderStatus, Prisma } from "@prisma/client";

export interface FilterParams {
  search?: string;
  status?: string;
  date?: string;
  sort?: string;
  page?: string;
  limit?: string;
}

export function buildQuery(params: FilterParams) {
  let orderByClause: Prisma.OrderOrderByWithRelationInput = {};
  let whereClause: Prisma.OrderWhereInput = {};

  // Parse pagination parameters
  const page = parseInt(params.page || "1", 10);
  const limit = parseInt(params.limit || "20", 10);
  const skip = (page - 1) * limit;

  // Search filter
  if (params.search) {
    whereClause.OR = [
      {
        id: {
          equals: isNaN(parseInt(params.search))
            ? undefined
            : parseInt(params.search),
        },
      },
      { buyer: { name: { contains: params.search, mode: "insensitive" } } },
      { buyer: { email: { contains: params.search, mode: "insensitive" } } },
    ];
  }

  // Status filter
  if (params.status && params.status !== "ALL") {
    whereClause.status = params.status as OrderStatus;
  }

  // Date filter
  if (params.date) {
    const filterDate = new Date(params.date);
    whereClause.createdAt = {
      gte: new Date(filterDate.setHours(0, 0, 0, 0)),
      lt: new Date(filterDate.setHours(23, 59, 59, 999)),
    };
  }

  // Sorting
  switch (params.sort) {
    case "newest":
      orderByClause.createdAt = "desc";
      break;
    case "oldest":
      orderByClause.createdAt = "asc";
      break;
    case "total-high":
      orderByClause.subTotal = "desc";
      break;
    case "total-low":
      orderByClause.subTotal = "asc";
      break;
    default:
      orderByClause.createdAt = "desc";
  }

  return {
    whereClause,
    orderByClause,
    page,
    limit,
    skip,
    search: params.search,
    status: params.status,
    date: params.date,
    sort: params.sort,
  };
}
