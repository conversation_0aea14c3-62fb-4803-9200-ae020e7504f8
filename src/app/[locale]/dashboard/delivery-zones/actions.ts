"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";
import { z } from "zod";

// Schema for delivery zone validation
const deliveryZoneSchema = z.object({
  name: z.string().min(1, "Zone name is required").max(100, "Zone name too long"),
  nam: z.string().max(100, "Bangla name too long").optional(),
  slug: z.string().min(1, "Slug is required").max(100, "Slug too long")
    .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  charge: z.number().int().min(0, "Charge must be non-negative"),
  express: z.number().int().min(0, "Express charge must be non-negative"),
  isBase: z.boolean(),
  parentId: z.string().optional(),
});

export type DeliveryZoneInput = z.infer<typeof deliveryZoneSchema>;

async function checkAdminPermission() {
  const currentUserId = await CookieUtil.userId();

  if (!currentUserId) {
    return ActionError("Login to Continue");
  }

  const prisma = getPrisma();

  const admin = await prisma.user.findUnique({
    where: {
      id: currentUserId,
      role: {
        in: [Role.SUPER_ADMIN, Role.ADMIN],
      },
    },
  });

  if (!admin) {
    return ActionError("Unauthorized");
  }

  return { prisma, admin };
}

export async function createDeliveryZone(data: DeliveryZoneInput) {
  try {
    const authResult = await checkAdminPermission();
    if ("error" in authResult) {
      return authResult;
    }

    const { prisma } = authResult;

    // Validate input data
    const validatedData = deliveryZoneSchema.parse(data);

    // Check if slug is unique
    const existingZone = await prisma.deliveryZone.findUnique({
      where: { slug: validatedData.slug },
    });

    if (existingZone) {
      return ActionError("A zone with this slug already exists");
    }

    // If parentId is provided, verify it exists
    if (validatedData.parentId) {
      const parentZone = await prisma.deliveryZone.findUnique({
        where: { id: validatedData.parentId },
      });

      if (!parentZone) {
        return ActionError("Parent zone not found");
      }
    }

    // Create the delivery zone
    const newZone = await prisma.deliveryZone.create({
      data: {
        name: validatedData.name,
        nam: validatedData.nam,
        slug: validatedData.slug,
        charge: validatedData.charge,
        express: validatedData.express,
        isBase: validatedData.isBase,
        parentId: validatedData.parentId,
      },
      include: {
        parentZone: true,
        subZones: true,
        _count: {
          select: {
            address: true,
          },
        },
      },
    });

    // Revalidate the delivery zones page
    revalidatePath("/dashboard/delivery-zones");

    return {
      success: true,
      message: "Delivery zone created successfully",
      zone: newZone,
    };
  } catch (error) {
    console.error("Failed to create delivery zone:", error);
    if (error instanceof z.ZodError) {
      return ActionError(error.errors[0].message);
    }
    return ActionError("Failed to create delivery zone");
  }
}

export async function updateDeliveryZone(zoneId: string, data: DeliveryZoneInput) {
  try {
    const authResult = await checkAdminPermission();
    if ("error" in authResult) {
      return authResult;
    }

    const { prisma } = authResult;

    // Validate input data
    const validatedData = deliveryZoneSchema.parse(data);

    // Check if zone exists
    const existingZone = await prisma.deliveryZone.findUnique({
      where: { id: zoneId },
      include: {
        subZones: true,
        _count: {
          select: {
            address: true,
          },
        },
      },
    });

    if (!existingZone) {
      return ActionError("Delivery zone not found");
    }

    // Check if slug is unique (excluding current zone)
    if (validatedData.slug !== existingZone.slug) {
      const slugExists = await prisma.deliveryZone.findFirst({
        where: {
          slug: validatedData.slug,
          id: { not: zoneId },
        },
      });

      if (slugExists) {
        return ActionError("A zone with this slug already exists");
      }
    }

    // If parentId is provided, verify it exists and doesn't create a cycle
    if (validatedData.parentId) {
      if (validatedData.parentId === zoneId) {
        return ActionError("A zone cannot be its own parent");
      }

      const parentZone = await prisma.deliveryZone.findUnique({
        where: { id: validatedData.parentId },
      });

      if (!parentZone) {
        return ActionError("Parent zone not found");
      }

      // Check for circular dependency
      const isDescendant = await checkIfDescendant(prisma, validatedData.parentId, zoneId);
      if (isDescendant) {
        return ActionError("Cannot set a descendant zone as parent (would create circular dependency)");
      }
    }

    // Update the delivery zone
    const updatedZone = await prisma.deliveryZone.update({
      where: { id: zoneId },
      data: {
        name: validatedData.name,
        nam: validatedData.nam,
        slug: validatedData.slug,
        charge: validatedData.charge,
        express: validatedData.express,
        isBase: validatedData.isBase,
        parentId: validatedData.parentId,
      },
      include: {
        parentZone: true,
        subZones: true,
        _count: {
          select: {
            address: true,
          },
        },
      },
    });

    // Revalidate the delivery zones page
    revalidatePath("/dashboard/delivery-zones");

    return {
      success: true,
      message: "Delivery zone updated successfully",
      zone: updatedZone,
    };
  } catch (error) {
    console.error("Failed to update delivery zone:", error);
    if (error instanceof z.ZodError) {
      return ActionError(error.errors[0].message);
    }
    return ActionError("Failed to update delivery zone");
  }
}

export async function deleteDeliveryZone(zoneId: string) {
  try {
    const authResult = await checkAdminPermission();
    if ("error" in authResult) {
      return authResult;
    }

    const { prisma } = authResult;

    // Check if zone exists
    const existingZone = await prisma.deliveryZone.findUnique({
      where: { id: zoneId },
      include: {
        subZones: true,
        _count: {
          select: {
            address: true,
          },
        },
      },
    });

    if (!existingZone) {
      return ActionError("Delivery zone not found");
    }

    // Check if zone has addresses
    if (existingZone._count.address > 0) {
      return ActionError("Cannot delete zone with existing addresses");
    }

    // Check if zone has sub-zones
    if (existingZone.subZones.length > 0) {
      return ActionError("Cannot delete zone with existing sub-zones");
    }

    // Delete the delivery zone
    await prisma.deliveryZone.delete({
      where: { id: zoneId },
    });

    // Revalidate the delivery zones page
    revalidatePath("/dashboard/delivery-zones");

    return {
      success: true,
      message: "Delivery zone deleted successfully",
    };
  } catch (error) {
    console.error("Failed to delete delivery zone:", error);
    return ActionError("Failed to delete delivery zone");
  }
}

// Helper function to check if a zone is a descendant of another zone
async function checkIfDescendant(prisma: any, potentialAncestorId: string, zoneId: string): Promise<boolean> {
  const zone = await prisma.deliveryZone.findUnique({
    where: { id: potentialAncestorId },
    select: { parentId: true },
  });

  if (!zone || !zone.parentId) {
    return false;
  }

  if (zone.parentId === zoneId) {
    return true;
  }

  return checkIfDescendant(prisma, zone.parentId, zoneId);
}
