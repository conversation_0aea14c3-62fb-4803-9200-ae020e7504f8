import { Product } from "@prisma/client";
import { manageCart } from "@udoy/actions/cart";
import { store } from "@udoy/state";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";

function useCartUtils(cachedProduct: Product) {
  async function handleAddToCart() {
    const snap = store.getSnapshot().context;
    const { count, product } = snap.cartItems[cachedProduct.id] || {};
    const supply = product?.supply || cachedProduct.supply;
    const itemCount = count || 0;
    const minOrder = (product as any)?.minOrder || (cachedProduct as any)?.minOrder || 1;

    if (itemCount >= supply) {
      return toast.error("পন্যটি আর অবশিষ্ট নেই");
    }

    // For new items, add minimum order quantity
    if (itemCount === 0) {
      // Add minimum order quantity at once
      for (let i = 0; i < minOrder; i++) {
        store.send({ type: "addToCart", product: cachedProduct });
      }
    } else {
      // Add one more if already in cart
      store.send({ type: "addToCart", product: cachedProduct });
    }

    try {
      await withError(manageCart(cachedProduct.id, "add"));
    } catch (error: any) {
      // Revert the changes
      if (itemCount === 0) {
        for (let i = 0; i < minOrder; i++) {
          store.send({ type: "removeFromCart", productId: cachedProduct.id });
        }
      } else {
        store.send({ type: "removeFromCart", productId: cachedProduct.id });
      }
    }
  }

  async function handleRemoveFromCart() {
    const snap = store.getSnapshot().context;
    const { count } = snap.cartItems[cachedProduct.id] || {};
    const itemCount = count || 0;
    const minOrder = (cachedProduct as any)?.minOrder || 1;

    // If removing would go below minimum, remove all
    if (itemCount <= minOrder) {
      for (let i = 0; i < itemCount; i++) {
        store.send({ type: "removeFromCart", productId: cachedProduct.id });
      }
    } else {
      store.send({ type: "removeFromCart", productId: cachedProduct.id });
    }

    try {
      await withError(manageCart(cachedProduct.id, "remove"));
    } catch (error: any) {
      // Revert the changes
      if (itemCount <= minOrder) {
        for (let i = 0; i < itemCount; i++) {
          store.send({ type: "addToCart", product: cachedProduct });
        }
      } else {
        store.send({ type: "addToCart", product: cachedProduct });
      }
    }
  }

  return {
    handleAddToCart,
    handleRemoveFromCart,
  };
}

export default useCartUtils;
