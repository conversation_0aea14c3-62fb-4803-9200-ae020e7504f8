"use client";

import React, { use<PERSON>em<PERSON>, useState, useEffect, use<PERSON>allback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@udoy/components/ui/select";
import { Card, CardContent } from "@udoy/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import { Label } from "@udoy/components/ui/label";
import { Separator } from "@udoy/components/ui/separator";
import { Switch } from "@udoy/components/ui/switch";
import { ImageUploader } from "../../components/image-uploader";
import { RichTextEditor } from "../../components/rich-text-editor";
import {
  ArrowLeft,
  Calendar,
  ChevronDown,
  <PERSON><PERSON>,
  <PERSON>,
  Save,
  Trash2,
} from "lucide-react";
import { toast } from "sonner";
import {
  CategoryWithSubcategories,
  ProductWithCategory,
} from "@udoy/utils/types";
import { Company, QuantityUnit, Shop } from "@prisma/client";
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@udoy/components/ui/command";
import Hide from "@udoy/components/Hide";
import { Link } from "@udoy/i18n/navigation";
import { withError } from "@udoy/utils/app-error";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import {
  productFormSchema,
  ProductFormValues,
} from "@dashboard/products/utils";
import { createProduct } from "../new/action";
import { Textarea } from "@udoy/components/ui/textarea";
import { updateProduct, cloneProduct } from "../[id]/edit/action";

// Define interfaces based on the Prisma schema
interface ProductFormProps {
  product?: ProductWithCategory;
  categories: CategoryWithSubcategories[];
  shops: Shop[];
  companies: Company[];
  quantityUnits: QuantityUnit[];
}

export function ProductForm({
  product,
  categories,
  shops,
  companies,
  quantityUnits,
}: ProductFormProps) {
  const update = Boolean(product?.id);
  const router = useRouter();
  const [commandOpen, setOpenCommand] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCloning, setIsCloning] = useState(false);
  const [images, setImages] = useState<{
    uploads: { file: File; id: string }[];
    removed: string[];
  }>({ uploads: [], removed: [] });

  // Function to generate slug from name
  const generateSlug = useCallback((name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }, []);

  // Initialize form with React Hook Form
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      id: product?.id,
      name: product?.name || "",
      nam: product?.nam || "",
      slug: product?.slug || "",
      description: product?.description || "",
      biboron: product?.biboron || "",
      titleBe: product?.titleBe || "",
      images: product?.images || [],

      categoryId: product?.categoryId || "",
      unitId: product?.unitId || quantityUnits[0]?.id || "",
      shopId: product?.shopId || "",

      quantity: product?.quantity || 1,
      minOrder: product?.minOrder || 1,
      maxOrder: product?.maxOrder || 10,
      amount: product?.amount || 1,
      supply: product?.supply || 100,
      extraCharge: product?.extraCharge || 0,
      companyId: product?.companyId || "",

      price: product?.price || 0,
      discount: product?.discount || 0,
      sourcePrice: product?.sourcePrice || 0,
      hide: Boolean(product?.hide),
      removedImages: [],

      position: product?.position || 0,
      featured: Boolean(product?.featured),
      alwaysAvailable: product?.alwaysAvailable ?? true,
      isHomeMade: Boolean((product as any)?.isHomeMade),
      isHigherPriced: Boolean((product as any)?.isHigherPriced),
    },
  });

  const category = useMemo(() => {
    const categoryId = form.watch("categoryId");

    // First check if it's a subcategory
    const subCategory = categories
      .flatMap((category) => category.subCategories)
      .find((subCategory) => subCategory.id === categoryId);

    if (subCategory) return subCategory;

    // Then check if it's a standalone base category
    return categories.find((category) => category.id === categoryId);
  }, [categories, form.watch("categoryId")]);

  // Auto-generate slug from product name when creating new product
  useEffect(() => {
    if (!update) {
      const subscription = form.watch((value, { name }) => {
        if (name === "name" && value.name && !value.slug) {
          const generatedSlug = generateSlug(value.name);
          form.setValue("slug", generatedSlug);
        }
      });
      return () => subscription.unsubscribe();
    }
  }, [form, update, generateSlug]);

  // Handle form submission
  const onSubmit = async (data: ProductFormValues) => {
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      images.uploads.forEach((image) => {
        formData.append("images", image.file);
      });

      const product = await withError(
        update
          ? updateProduct(
              {
                ...data,
                removedImages: images.removed,
              },
              formData
            )
          : createProduct(data, formData)
      );
      toast.success(update ? "Product Updated" : "Product Created");
    } catch (error: any) {
      toast.error(
        error?.message || update
          ? "Failed to update product"
          : "Failed to create product"
      );
    } finally {
      setIsSubmitting(false);
      if (!update) {
        form.reset();
        setImages({ uploads: [], removed: [] });
      }
    }
  };

  // Handle product cloning
  const handleClone = async () => {
    if (!product?.id) return;

    setIsCloning(true);
    try {
      const clonedProduct = await withError(cloneProduct(product.id));
      toast.success("Product cloned successfully");
      router.push(`/dashboard/products/${clonedProduct.id}/edit`);
    } catch (error: any) {
      toast.error(error?.message || "Failed to clone product");
    } finally {
      setIsCloning(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        onError={console.log}
        className="space-y-8"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">
              {update ? "Edit Product" : "New Product"}
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <Hide open={update}>
              <Button asChild type="button" variant="outline">
                <Link href={`/dashboard/products/${product?.id}/availability`}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Availability
                </Link>
              </Button>
              <Button asChild type="button" variant="outline">
                <Link href={`/dashboard/products/${product?.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View
                </Link>
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleClone}
                disabled={isCloning}
              >
                <Copy className="mr-2 h-4 w-4" />
                {isCloning ? "Cloning..." : "Clone"}
              </Button>
              {/* <Button
                type="button"
                variant="destructive"
                onClick={() => toast.info("Not Implemented Yet")}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button> */}
            </Hide>
            <Button type="submit" disabled={isSubmitting}>
              <Save className="mr-2 h-4 w-4" />
              {isSubmitting
                ? update
                  ? "Updating..."
                  : "Creating..."
                : update
                ? "Update"
                : "Create"}
            </Button>
          </div>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          {/* Main product information */}
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter product name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="nam"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Name {"(Bangla)"}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter product name in Bengali"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="titleBe"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Title {"(Bengali)"}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter product title in Bengali"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Slug</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="product-slug-url"
                            {...field}
                            onChange={(e) => {
                              // Auto-generate slug from input
                              const value = e.target.value
                                .toLowerCase()
                                .replace(/[^a-z0-9\s-]/g, '')
                                .replace(/\s+/g, '-')
                                .replace(/-+/g, '-');
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                        <p className="text-xs text-muted-foreground">
                          URL-friendly version of the product name. Only lowercase letters, numbers, and hyphens allowed.
                        </p>
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Product Description {"(English)"}
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              rows={6}
                              placeholder="Enter product name in English"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="biboron"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Product Description {"(Bangla)"}
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              rows={6}
                              placeholder="Enter product name in Bengali"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Product Images</h3>
                <ImageUploader
                  images={images}
                  existing={product?.images || []}
                  onChange={setImages}
                />
              </CardContent>
            </Card>
          </div>

          {/* Product details sidebar */}
          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Product Details</h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <FormControl>
                          <Button
                            type="button"
                            onClick={() => setOpenCommand(true)}
                            className="w-full justify-between"
                            variant="outline"
                          >
                            <span>
                              {category?.name ||
                                "Select a category from the dropdown"}
                            </span>
                            <ChevronDown className="h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                        <CommandDialog
                          open={commandOpen}
                          onOpenChange={setOpenCommand}
                        >
                          <CommandInput placeholder="Search category with name..." />
                          <CommandList className="w-full">
                            <CommandEmpty>No results found.</CommandEmpty>

                            {/* Show standalone base categories first */}
                            {categories.filter(cat => cat.isBase && cat.subCategories.length === 0).length > 0 && (
                              <CommandGroup heading="Base Categories">
                                {categories
                                  .filter(cat => cat.isBase && cat.subCategories.length === 0)
                                  .map((category) => (
                                    <CommandItem
                                      key={category.id}
                                      onSelect={() => {
                                        form.setValue("categoryId", category.id);
                                        setOpenCommand(false);
                                      }}
                                    >
                                      {category.name}
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            )}

                            {/* Show categories with subcategories */}
                            {categories
                              .filter(cat => cat.subCategories.length > 0)
                              .map((category) => (
                                <CommandGroup key={category.id} heading={category.name}>
                                  {category.subCategories.map((item) => (
                                    <CommandItem
                                      key={item.id}
                                      onSelect={() => {
                                        form.setValue("categoryId", item.id);
                                        setOpenCommand(false);
                                      }}
                                    >
                                      {item.name}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              ))}
                          </CommandList>
                        </CommandDialog>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Selling Price</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                                ৳
                              </span>
                              <Input
                                type="number"
                                min={0}
                                className="pl-7"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="sourcePrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Source Price</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                                ৳
                              </span>
                              <Input
                                type="number"
                                min={0}
                                className="pl-7"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          {/* <FormDescription>
                            This is your cost price. It will not be shown to
                            customers.
                          </FormDescription> */}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="discount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Discount Amount</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                                ৳
                              </span>
                              <Input
                                type="number"
                                min={0}
                                className="pl-7"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Hide Product Option with Select */}
                    <FormField
                      control={form.control}
                      name="hide"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Hide Product</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value ? "true" : "false"}
                              onValueChange={(value) =>
                                field.onChange(value === "true")
                              }
                            >
                              <SelectTrigger className="">
                                <SelectValue placeholder="Hide Product" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">Hide</SelectItem>
                                <SelectItem value="false">Show</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <div className="grid gap-2 grid-cols-2">
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Amount</FormLabel>
                          <div className="flex items-center gap-2">
                            <FormControl>
                              <Input type="number" min={0} {...field} />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="unitId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Unit</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <SelectTrigger className="flex-1">
                                <SelectValue placeholder="Unit" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  <SelectLabel>Units</SelectLabel>
                                  {quantityUnits.map((unit) => (
                                    <SelectItem key={unit.id} value={unit.id}>
                                      {unit.full} ({unit.slug})
                                    </SelectItem>
                                  ))}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="quantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quantity</FormLabel>
                          <FormControl>
                            <Input type="number" min={0} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="supply"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Stock</FormLabel>
                          <FormControl>
                            <Input type="number" min={0} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="minOrder"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormLabel>Min Quantity Per Order</FormLabel>
                          <FormControl>
                            <Input type="number" min={1} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxOrder"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormLabel>Max Quantity Per Order</FormLabel>
                          <FormControl>
                            <Input type="number" min={1} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="extraCharge"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormLabel>Extra Charge</FormLabel>
                          <FormControl>
                            <Input type="number" min={0} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <FormField
                    control={form.control}
                    name="shopId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shop {"(Optional)"}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a shop" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {shops.map((shop) => (
                              <SelectItem key={shop.id} value={shop.id}>
                                {shop.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="companyId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company {"(Optional)"}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a company" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {companies.map((company) => (
                              <SelectItem key={company.id} value={company.id}>
                                {company.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="position"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Position</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              {...field}
                              onChange={(e) =>
                                field.onChange(parseInt(e.target.value) || 0)
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="featured"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Featured</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value ? "true" : "false"}
                              onValueChange={(value) =>
                                field.onChange(value === "true")
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select option" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">Yes</SelectItem>
                                <SelectItem value="false">No</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="alwaysAvailable"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Always Available</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value ? "true" : "false"}
                            onValueChange={(value) =>
                              field.onChange(value === "true")
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select availability" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="true">Always Available</SelectItem>
                              <SelectItem value="false">Use Availability Schedule</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="isHomeMade"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Home Made Product</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value ? "true" : "false"}
                              onValueChange={(value) =>
                                field.onChange(value === "true")
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select option" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">Yes</SelectItem>
                                <SelectItem value="false">No</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isHigherPriced"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Higher Priced Product</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value ? "true" : "false"}
                              onValueChange={(value) =>
                                field.onChange(value === "true")
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select option" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">Yes</SelectItem>
                                <SelectItem value="false">No</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </Form>
  );
}
