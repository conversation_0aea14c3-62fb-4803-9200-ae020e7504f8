import { Suspense } from "react";
import { getPrisma } from "@udoy/utils/db-utils";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@udoy/components/ui/card";
import { Button } from "@udoy/components/ui/button";
import { Plus } from "lucide-react";
import Layout from "../components/Layout";
import { DeliveryZonesList } from "./components/delivery-zones-list";
import { CreateZoneDialog } from "./components/create-zone-dialog";

async function getDeliveryZones() {
  const prisma = getPrisma();

  // Get only parent zones (zones without parentId)
  const zones = await prisma.deliveryZone.findMany({
    where: { parentId: null },
    include: {
      _count: {
        select: {
          address: true,
        },
      },
    },
    orderBy: {
      name: "asc",
    },
  });

  // Get all zones for parent selection in dialogs
  const allZones = await prisma.deliveryZone.findMany({
    orderBy: [{ name: "asc" }],
  });

  return { zones, allZones };
}

export default async function DeliveryZonesPage() {
  const { zones, allZones } = await getDeliveryZones();

  return (
    <Layout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Delivery Zones</h1>
            <p className="text-muted-foreground">
              Manage delivery zones and their charges
            </p>
          </div>
          <CreateZoneDialog zones={allZones}>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Zone
            </Button>
          </CreateZoneDialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Delivery Zones</CardTitle>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading zones...</div>}>
              <DeliveryZonesList zones={zones} allZones={allZones} />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
