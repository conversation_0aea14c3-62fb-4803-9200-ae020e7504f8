import { getPrisma } from "@udoy/utils/db-utils";
import CategoryItem from "../../components/CategoryItem";
import { Category } from "@prisma/client";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { Cache<PERSON><PERSON> } from "@udoy/utils/cache-key";
import Locale from "@udoy/components/Locale";

async function getPopularCategoriesBySales(): Promise<Category[]> {
  "use cache";
  cacheTag(CacheKey.MostPopularCategories());
  cacheLife("days");

  const prisma = getPrisma();
  // 1. Aggregate total quantity sold per product
  const productSalesQuantity = await prisma.orderItem.groupBy({
    by: ["productId"],
    _sum: {
      quantity: true, // Sum the quantity for each product
    },
  });

  // Create a map for easy lookup: ProductID -> TotalQuantitySold
  const productQuantityMap = new Map<string, number>();
  productSalesQuantity.forEach((item) => {
    // Ensure quantity is not null (it shouldn't be based on schema, but good practice)
    productQuantityMap.set(item.productId, item._sum.quantity ?? 0);
  });

  // 2. Fetch all base categories with their product IDs and parent IDs
  const baseCategories = await prisma.category.findMany({
    where: {
      isBase: true,
      hide: false,
      // Optimization: Only include categories whose products HAVE sold,
      // though the map lookup handles products with 0 sales anyway.
      // products: { some: { id: { in: Array.from(productQuantityMap.keys()) } } }
    },
    select: {
      id: true,
      parentId: true, // Need the parent ID to aggregate later
      parentCategory: {
        where: {
          hide: false,
        },
      },
      products: {
        where: {
          hide: false,
        },
        select: {
          id: true, // Only need product IDs associated with the base category
        },
      },
    },
  });

  // 3 & 4. Calculate sales per base category and aggregate by parent category
  const parentCategorySales = new Map<string, number>();

  for (const baseCat of baseCategories) {
    if (baseCat.parentId) {
      let totalSalesForBaseCategory = 0;
      // Sum sales for all products within this base category
      for (const product of baseCat.products) {
        totalSalesForBaseCategory += productQuantityMap.get(product.id) ?? 0;
      }

      // Add this base category's sales to its parent's total
      const currentParentSales = parentCategorySales.get(baseCat.parentId) ?? 0;
      parentCategorySales.set(
        baseCat.parentId,
        currentParentSales + totalSalesForBaseCategory
      );
    }
    // else: Base category has no parent, decide if you want to handle these separately
  }

  // 5. Sort parent categories by total sales (descending) and get top IDs
  const sortedParentIds = Array.from(parentCategorySales.entries())
    .sort(([, salesA], [, salesB]) => salesB - salesA) // Sort descending by sales
    .map(([id]) => id); // Get only the IDs

  const topParentIds = sortedParentIds; // Take the top 6

  // 6. Fetch the details of the top parent categories
  if (topParentIds.length === 0) {
    return []; // No sales data or no parent categories with sales
  }

  const popularParentCategories = await prisma.category.findMany({
    where: {
      hide: false,
      id: {
        in: topParentIds,
      },
    },
    take: 6,
  });

  // Optional: Re-sort the final results to match the sales order,
  // as `findMany` with `in` doesn't guarantee order.
  popularParentCategories.sort(
    (a, b) => topParentIds.indexOf(a.id) - topParentIds.indexOf(b.id)
  );

  return popularParentCategories;
}

async function PopularCategories() {
  const categories = await getPopularCategoriesBySales();
  return (
    <div className="mt-8 text-2xl font-bold w-full">
      <h3 className="text-center">
        <Locale bn="সবচেয়ে বিক্রিত ক্যাটাগরি">Most Popular Categories</Locale>
      </h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 mt-6 xl:grid-cols-6 px-6 gap-4 w-full">
        {categories.map((category) => (
          <CategoryItem category={category} key={category.id} />
        ))}
      </div>
    </div>
  );
}

export default PopularCategories;
