"use client";

import { But<PERSON> } from "@udoy/components/ui/button";
import { Edit } from "lucide-react";
import Link from "next/link";
import useIsAdmin from "@udoy/hooks/useIsAdmin";

interface AdminEditLinkProps {
  productId: string;
  className?: string;
}

export function AdminEditLink({ productId, className }: AdminEditLinkProps) {
  const isAdmin = useIsAdmin();

  if (!isAdmin) {
    return null;
  }

  return (
    <Button size="sm" variant="outline" asChild className={className}>
      <Link href={`/dashboard/products/${productId}/edit`}>
        <Edit className="h-4 w-4" />
      </Link>
    </Button>
  );
}
