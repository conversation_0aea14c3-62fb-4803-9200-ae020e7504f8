export enum DashboardStatType {
  REVENUE_TOTAL = "revenue_total",
  REVENUE_CHANGE = "revenue_change",
  ORDERS_TOTAL = "orders_total",
  ORDERS_CHANGE = "orders_change",
  DELIVERY_TIME_AVG = "delivery_time_avg",
  DELIVERY_TIME_CHANGE = "delivery_time_change",
  CUSTOMERS_ACTIVE = "customers_active",
  CUSTOMERS_CHANGE = "customers_change",
  RECENT_ORDERS = "recent_orders",
}

export class CacheKey {
  static ShopPage(slug?: string) {

    if (!slug) {
      return `CLIENT::SHOP_PAGE_CACHE::ALL`;
    }

    return `CLIENT::SHOP_PAGE_CACHE::${slug}`;
  }

  static UserOrder(orderId: string) {
    return `CLIENT::ORDER_PAGE_CACHE::${orderId}`;
  }

  static MostPopularCategories() {
    return `CLIENT::LANDING_PAGE_CACHE::MOST_POPULAR_CATEGORIES`;
  }

  static MostPopularProducts() {
    return `CLIENT::LANDING_PAGE_CACHE::MOST_POPULAR_PRODUCTS`;
  }

  static DashboardStats(type?: DashboardStatType | string) {
    if (type) {
      return `DASHBOARD::STATS::${type.toUpperCase()}`;
    }
    return `DASHBOARD::STATS::ALL`;
  }

  static SidebarCategory() {
    return `CLIENT::SIDEBAR_CACHE::CATEGORIES`;
  }

  static CategoryProducts(categorySlug: string) {
    return `CLIENT::CATEGORY_PRODUCTS_CACHE::${categorySlug}`;
  }

  static AllCategoryProducts() {
    return `CLIENT::CATEGORY_PRODUCTS_CACHE::ALL`;
  }
}
