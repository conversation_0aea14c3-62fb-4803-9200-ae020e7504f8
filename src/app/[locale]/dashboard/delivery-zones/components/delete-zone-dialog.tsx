"use client";

import { useState } from "react";
import { Prisma } from "@prisma/client";
import { But<PERSON> } from "@udoy/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { AlertTriangle } from "lucide-react";
import { deleteDeliveryZone } from "../actions";
import { toast } from "sonner";
import { isActionError } from "@udoy/utils/app-error";

type DeliveryZoneWithRelations = Prisma.DeliveryZoneGetPayload<{
  include: {
    subZones: true;
    parentZone: true;
    _count: {
      select: {
        address: true;
      };
    };
  };
}>;

interface DeleteZoneDialogProps {
  zone: DeliveryZoneWithRelations;
  isOpen: boolean;
  onClose: () => void;
}

export function DeleteZoneDialog({ zone, isOpen, onClose }: DeleteZoneDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const canDelete = zone._count.address === 0 && zone.subZones.length === 0;

  const handleDelete = async () => {
    if (!canDelete) {
      toast.error("Cannot delete zone with existing addresses or sub-zones");
      return;
    }

    setIsLoading(true);

    try {
      const result = await deleteDeliveryZone(zone.id);

      if (isActionError(result)) {
        toast.error(result.error.message);
      } else {
        toast.success("Delivery zone deleted successfully");
        onClose();
        // The page will be revalidated by the server action
        window.location.reload();
      }
    } catch (error) {
      toast.error("Failed to delete delivery zone");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Delete Delivery Zone
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the delivery zone &quot;{zone.name}&quot;?
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {!canDelete && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-md p-4 mb-4">
              <h4 className="font-medium text-destructive mb-2">Cannot Delete Zone</h4>
              <ul className="text-sm text-destructive space-y-1">
                {zone._count.address > 0 && (
                  <li>• Zone is used by {zone._count.address} address(es)</li>
                )}
                {zone.subZones.length > 0 && (
                  <li>• Zone has {zone.subZones.length} sub-zone(s)</li>
                )}
              </ul>
              <p className="text-sm text-destructive mt-2">
                Please remove all addresses and sub-zones before deleting this zone.
              </p>
            </div>
          )}

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Zone Name:</span>
              <span className="font-medium">{zone.name}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Slug:</span>
              <span className="font-mono">{zone.slug}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Addresses:</span>
              <span>{zone._count.address}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Sub-zones:</span>
              <span>{zone.subZones.length}</span>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading || !canDelete}
          >
            {isLoading ? "Deleting..." : "Delete Zone"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
